package com.taobao.wireless.orange.service;

import com.taobao.unifiedsession.core.json.JSON;
import com.taobao.wireless.orange.common.annotation.AttributeValidate;
import com.taobao.wireless.orange.common.constant.enums.OperationType;
import com.taobao.wireless.orange.common.constant.enums.ReleaseOrderStatus;
import com.taobao.wireless.orange.common.model.Pagination;
import com.taobao.wireless.orange.common.model.PaginationResult;
import com.taobao.wireless.orange.common.model.Result;
import com.taobao.wireless.orange.common.util.BeanUtil;
import com.taobao.wireless.orange.common.util.Pipe;
import com.taobao.wireless.orange.dal.enhanced.dao.OConditionDAO;
import com.taobao.wireless.orange.dal.enhanced.dao.OReleaseOrderDAO;
import com.taobao.wireless.orange.dal.enhanced.entity.OConditionDO;
import com.taobao.wireless.orange.manager.ReleaseOrderManager;
import com.taobao.wireless.orange.manager.model.*;
import com.taobao.wireless.orange.manager.util.PageUtil;
import com.taobao.wireless.orange.service.mapper.ConditionMapper;
import com.taobao.wireless.orange.service.model.*;
import jakarta.annotation.Nullable;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.taobao.wireless.orange.common.constant.Common.DEFAULT_CONDITION_ID;
import static com.taobao.wireless.orange.common.constant.Common.DEFAULT_CONDITION_NAME;

@Service
public class ReleaseOrderService {
    @Autowired
    private ReleaseOrderManager releaseOrderManager;
    @Autowired
    private ParameterService parameterService;
    @Autowired
    private ConditionService conditionService;
    @Autowired
    private OConditionDAO conditionDAO;
    @Autowired
    private OReleaseOrderDAO releaseOrderDAO;

    /**
     * 查询发布单列表
     *
     * @param query      发布单查询条件
     * @param pagination 分页信息
     * @return 发布单列表
     */
    @AttributeValidate
    public PaginationResult<ReleaseOrderDTO> query(@NotNull(message = "发布单查询条件不能为空") ReleaseOrderQueryDTO query,
                                                   @NotNull(message = "分页信息不能为空") Pagination pagination) {
        return Pipe.of(query)
                .map(BeanUtil.createFromProperties(ReleaseOrderBO.class))
                .map(q -> releaseOrderManager.query(q, pagination))
                .map(PageUtil.convertToPaginationResult(ReleaseOrderDTO.class))
                .get();
    }

    /**
     * 获取发布单状态统计
     *
     * @param namespaceId 命名空间ID
     * @return 发布单状态统计
     */
    @AttributeValidate
    public Result<Map<ReleaseOrderStatus, Long>> getCountGroupByStatus(String namespaceId) {
        return Pipe.of(namespaceId)
                .map(releaseOrderDAO::getCountGroupByStatus)
                .map(Result::new)
                .get();
    }

    /**
     * 获取发布单详情
     *
     * @param releaseVersion 发布版本号
     * @return 发布单详情
     */
    @AttributeValidate
    public Result<ReleaseOrderDetailDTO> getDetail(@NotBlank(message = "发布版本号不能为空") String releaseVersion) {
        return Pipe.of(releaseVersion)
                .map(releaseOrderManager::getDetail)
                .map(this::convert)
                .map(Result::new)
                .get();
    }

    /**
     * 获取发布单变更内容
     *
     * @param releaseVersion 发布版本号
     * @return 发布单变更内容
     */
    @AttributeValidate
    public Result<ReleaseOrderChangesDTO> getChanges(@NotBlank(message = "发布版本号不能为空") String releaseVersion) {
        return Pipe.of(releaseVersion)
                .map(releaseOrderManager::getChanges)
                .map(this::convert)
                .map(Result::new)
                .get();
    }

    /**
     * 获取发布单操作记录
     *
     * @param releaseVersion 发布版本号
     * @param operationTypes 操作类型列表
     * @return 发布单操作记录
     */
    @AttributeValidate
    public Result<List<ReleaseOrderOperationDTO>> getOperations(@NotBlank(message = "发布版本号不能为空") String releaseVersion,
                                                                @Nullable List<OperationType> operationTypes) {
        return Pipe.of(releaseOrderManager.getOperations(releaseVersion, operationTypes))
                .map(BeanUtil.createListFromProperties(ReleaseOrderOperationDTO.class))
                .map(Result::new)
                .get();
    }

    /**
     * 创建发布单
     *
     * @param releaseOrder 发布单创建条件
     * @return 发布版本号
     */
    @AttributeValidate
    public Result<String> create(@NotNull(message = "发布单创建条件不能为空") ReleaseOrderCreateDTO releaseOrder) {
        return Pipe.of(releaseOrder)
                .map(this::convert)
                .map(releaseOrderManager::create)
                .map(Result::new)
                .get();
    }

    @AttributeValidate
    public Result<Void> applyRelease(String releaseVersion, @NotNull(message = "发布申请不能为空") ApplyReleaseDTO applyRelease) {
        releaseOrderManager.applyRelease(releaseVersion, BeanUtil.createFromProperties(applyRelease, ApplyReleaseBO.class));
        return Result.success();
    }

    @AttributeValidate
    public Result<Void> publish(String releaseVersion) {
        releaseOrderManager.publish(releaseVersion);
        return Result.success();
    }

    @AttributeValidate
    public Result<Void> cancel(String releaseVersion) {
        releaseOrderManager.cancel(releaseVersion);
        return Result.success();
    }

    @AttributeValidate
    public Result<Void> ratioGray(String releaseVersion, RatioGrayDTO ratioGray) {
        releaseOrderManager.ratioGray(releaseVersion, ratioGray.getPercent());
        return Result.success();
    }

    @AttributeValidate
    public Result<Void> verifyReply(String releaseVersion, @NotNull(message = "验证回复不能为空") VerifyReplyDTO verifyReplyDTO) {
        releaseOrderManager.verifyReply(releaseVersion,
                BeanUtil.createFromProperties(verifyReplyDTO, VerifyReplyBO.class));
        return Result.success();
    }

    @AttributeValidate
    public Result<Void> startVerify(String releaseVersion) {
        releaseOrderManager.startVerify(releaseVersion);
        return Result.success();
    }

    @AttributeValidate
    public Result<DebugInfoDTO> getDebugInfo(@NotBlank(message = "发布版本号不能为空") String releaseVersion) {
        return Pipe.of(releaseVersion)
                .map(releaseOrderManager::getDebugInfo)
                .map(BeanUtil.createFromProperties(DebugInfoDTO.class))
                .map(Result::new)
                .get();
    }

    private ReleaseOrderChangesDTO convert(ReleaseOrderChangeBO parameterChanges) {
        ReleaseOrderChangesDTO releaseOrderChangesDTO = BeanUtil.createFromProperties(parameterChanges, ReleaseOrderChangesDTO.class);
        releaseOrderChangesDTO.setPreviousParametersDetail(parameterService.convert(parameterChanges.getNamespaceId(), parameterChanges.getPreviousParameters()));
        releaseOrderChangesDTO.setPreviousConditionsDetail(conditionService.convertToDetailDTO(parameterChanges.getPreviousConditions()));
        releaseOrderChangesDTO.setParameterChanges(convertToParameterDetailDTO(parameterChanges.getParameterChanges()));
        releaseOrderChangesDTO.setConditionChanges(convertToConditionDetailDTO(parameterChanges.getConditionChanges()));
        return releaseOrderChangesDTO;
    }

    private List<ParameterDetailDTO> convertToParameterDetailDTO(List<ParameterVersionBO> parameterVersions) {
        List<String> conditionIds = parameterVersions.stream()
                .filter(p -> CollectionUtils.isNotEmpty(p.getParameterConditionVersions()))
                .flatMap(p -> p.getParameterConditionVersions().stream())
                .map(ParameterConditionVersionBO::getConditionId)
                .distinct()
                .toList();
        Map<String, OConditionDO> conditionId2Condition = conditionDAO.getConditionMap(conditionIds);
        return parameterVersions.stream().map(p -> {
            ParameterDetailDTO parameterDetailDTO = BeanUtil.createFromProperties(p, ParameterDetailDTO.class);
            if (CollectionUtils.isEmpty(parameterDetailDTO.getParameterConditions())) {
                return parameterDetailDTO;
            }

            List<ParameterConditionDTO> parameterConditions = BeanUtil.createListFromProperties(p.getParameterConditionVersions(), ParameterConditionDTO.class)
                    .stream()
                    .peek(c -> {
                        if (DEFAULT_CONDITION_ID.equals(c.getConditionId())) {
                            c.setConditionName(DEFAULT_CONDITION_NAME);
                        } else {
                            var condition = conditionId2Condition.get(c.getConditionId());
                            c.setConditionName(condition.getName());
                            c.setConditionColor(condition.getColor());
                        }
                    })
                    .toList();
            parameterDetailDTO.setParameterConditions(parameterConditions);
            return parameterDetailDTO;
        }).toList();
    }

    private List<ConditionDetailDTO> convertToConditionDetailDTO(List<ConditionVersionBO> conditionVersions) {
        return conditionVersions.stream().map(c -> {
            ConditionDetailDTO conditionDetailDTO = BeanUtil.createFromProperties(c, ConditionDetailDTO.class);
            conditionDetailDTO.setExpression(JSON.parse(c.getExpression(), ConditionExpressionDTO.class));
            conditionDetailDTO.setName(c.getCondition().getName());
            conditionDetailDTO.setColor(c.getCondition().getColor());
            return conditionDetailDTO;
        }).toList();
    }

    private ReleaseOrderBO convert(ReleaseOrderCreateDTO releaseOrder) {
        ReleaseOrderBO releaseOrderBO = BeanUtil.createFromProperties(releaseOrder, ReleaseOrderBO.class);

        List<ParameterChangeDTO> parameterChanges = releaseOrder.getParameterChanges();
        if (CollectionUtils.isNotEmpty(parameterChanges)) {
            List<ParameterVersionBO> parameterVersionBOS = parameterChanges.stream().map(parameterChangeDTO -> {
                ParameterVersionBO parameterVersionBO = BeanUtil.createFromProperties(parameterChangeDTO, ParameterVersionBO.class);
                parameterVersionBO.setParameterBO(BeanUtil.createFromProperties(parameterChangeDTO, ParameterBO.class));
                parameterVersionBO.setConditionNamesOrder(parameterChangeDTO.getConditionNamesOrder());

                List<ParameterConditionChangeDTO> parameterConditionChanges = parameterChangeDTO.getParameterConditionChanges();
                if (CollectionUtils.isNotEmpty(parameterConditionChanges)) {
                    List<ParameterConditionVersionBO> parameterConditionVersionBOS = BeanUtil.createListFromProperties(parameterConditionChanges, ParameterConditionVersionBO.class);
                    parameterVersionBO.setParameterConditionVersions(parameterConditionVersionBOS);
                }

                return parameterVersionBO;
            }).collect(Collectors.toList());
            releaseOrderBO.setParameterVersions(parameterVersionBOS);
        }

        List<ConditionChangeDTO> conditionChanges = releaseOrder.getConditionChanges();
        List<ConditionVersionBO> conditionVersionBOS = CollectionUtils.isNotEmpty(conditionChanges) ? conditionChanges.stream().map(conditionChangeDTO -> {
            ConditionVersionBO conditionVersionBO = BeanUtil.createFromProperties(conditionChangeDTO, ConditionVersionBO.class);
            conditionVersionBO.setCondition(BeanUtil.createFromProperties(conditionChangeDTO, OConditionDO.class));
            conditionVersionBO.setExpression(JSON.toJSONString(conditionChangeDTO.getExpression()));
            return conditionVersionBO;
        }).collect(Collectors.toList()) : new ArrayList<>();
        releaseOrderBO.setConditionVersions(conditionVersionBOS);

        return releaseOrderBO;
    }

    private ReleaseOrderDetailDTO convert(ReleaseOrderBO releaseOrder) {
        ReleaseOrderDetailDTO releaseOrderDetailDTO = BeanUtil.createFromProperties(releaseOrder, ReleaseOrderDetailDTO.class);
        if (CollectionUtils.isNotEmpty(releaseOrder.getParameterVersions())) {
            releaseOrderDetailDTO.setParameterKeys(releaseOrder.getParameterVersions().stream().map(ParameterVersionBO::getParameterKey).collect(Collectors.toList()));
        }
        if (CollectionUtils.isNotEmpty(releaseOrder.getConditionVersions())) {
            releaseOrderDetailDTO.setConditionIds(releaseOrder.getConditionVersions().stream().map(ConditionVersionBO::getConditionId).collect(Collectors.toList()));
        }
        return releaseOrderDetailDTO;
    }
}
