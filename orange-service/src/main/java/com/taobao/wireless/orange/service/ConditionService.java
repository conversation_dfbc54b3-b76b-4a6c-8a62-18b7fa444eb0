package com.taobao.wireless.orange.service;


import com.taobao.unifiedsession.core.json.JSON;
import com.taobao.wireless.orange.common.annotation.AttributeValidate;
import com.taobao.wireless.orange.common.model.Pagination;
import com.taobao.wireless.orange.common.model.PaginationResult;
import com.taobao.wireless.orange.common.model.Result;
import com.taobao.wireless.orange.common.util.BeanUtil;
import com.taobao.wireless.orange.common.util.Pipe;
import com.taobao.wireless.orange.dal.enhanced.dao.OConditionDAO;
import com.taobao.wireless.orange.dal.enhanced.entity.OConditionDO;
import com.taobao.wireless.orange.manager.ConditionManager;
import com.taobao.wireless.orange.manager.model.ConditionBO;
import com.taobao.wireless.orange.service.mapper.ConditionMapper;
import com.taobao.wireless.orange.service.model.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class ConditionService {

    @Autowired
    private ConditionManager conditionManager;

    @Autowired
    private OConditionDAO conditionDAO;

    /**
     * 更新条件
     *
     * @param condition 条件更新对象
     */
    @AttributeValidate
    public Result<Void> update(@NotNull(message = "条件更新对象不能为空") ConditionDirectUpdateDTO condition) {
        conditionDAO.lambdaUpdate()
                .eq(OConditionDO::getConditionId, condition.getConditionId())
                .set(StringUtils.isNotBlank(condition.getName()), OConditionDO::getName, condition.getName())
                .set(StringUtils.isNotBlank(condition.getColor()), OConditionDO::getColor, condition.getColor())
                .update();
        return Result.success();
    }

    @AttributeValidate
    public Result<Map<String, ConditionDTO>> getConditionMapByIds(@NotEmpty(message = "条件ID列表不能为空") List<String> conditionIds) {
        return Pipe.of(conditionIds)
                .map(conditionDAO::getConditionMap)
                .map(m -> m.entrySet().stream()
                        .collect(Collectors.toMap(Map.Entry::getKey,
                                e -> BeanUtil.createFromProperties(e.getValue(), ConditionDTO.class))))
                .map(Result::new)
                .get();
    }

    @AttributeValidate
    public PaginationResult<ConditionDetailDTO> query(@NotNull(message = "条件查询条件不能为空") ConditionQueryDTO query, Pagination pagination) {
        return Pipe.of(query)
                .map(BeanUtil.createFromProperties(ConditionBO.class))
                .map(q -> conditionManager.query(q, pagination))
                .map(r -> {
                    PaginationResult<ConditionDetailDTO> result = BeanUtil.createFromProperties(r, PaginationResult.class);
                    result.setData(convertToDetailDTO(r.getRecords()));
                    return result;
                })
                .get();
    }

    /**
     * 获取所有条件(用户下拉选择)
     *
     * @param query
     * @return
     */
    @AttributeValidate
    public Result<List<ConditionDTO>> getAll(@NotNull(message = "条件查询条件不能为空") ConditionQueryDTO query) {
        return Pipe.of(query)
                .map(BeanUtil.createFromProperties(ConditionBO.class))
                .map(q -> conditionManager.getAllOnlineConditions(q))
                .map(this::convert)
                .map(Result::new)
                .get();
    }

    /**
     * 获取条件线上版本的详情
     *
     * @param conditionId 条件ID
     * @return
     */
    @AttributeValidate
    public Result<ConditionDetailDTO> getByConditionId(@NotBlank(message = "条件ID不能为空") String conditionId) {
        return Pipe.of(conditionId)
                .map(conditionManager::getOnlineConditionDetailByConditionId)
                .map(c -> {
                    ConditionDetailDTO condition = BeanUtil.createFromProperties(c, ConditionDetailDTO.class);
                    condition.setExpression(JSON.parse(c.getConditionVersion().getExpression(), ConditionExpressionDTO.class));
                    return condition;
                })
                .map(Result::new)
                .get();
    }

    public List<ConditionDetailDTO> convertToDetailDTO(List<ConditionBO> conditions) {
        return conditions.stream().map(c -> {
            ConditionDetailDTO detail = BeanUtil.createFromProperties(c, ConditionDetailDTO.class);
            detail.setParameterConditions(BeanUtil.createListFromProperties(c.getRelatedParameters(), ParameterConditionDTO.class));

            var conditionVersion = c.getConditionVersion();
            detail.setGmtModified(conditionVersion.getGmtCreate());
            detail.setExpression(JSON.parse(conditionVersion.getExpression(), ConditionExpressionDTO.class));
            return detail;
        }).collect(Collectors.toList());
    }

    private List<ConditionDTO> convert(List<ConditionBO> conditions) {
        return conditions.stream().map(c -> {
            var conditionVersion = c.getConditionVersion();
            ConditionDTO condition = BeanUtil.createFromProperties(conditionVersion, ConditionDTO.class);
            condition.setExpression(JSON.parse(conditionVersion.getExpression(), ConditionExpressionDTO.class));

            condition.setName(c.getName());
            condition.setColor(c.getColor());
            return condition;
        }).collect(Collectors.toList());
    }
}
